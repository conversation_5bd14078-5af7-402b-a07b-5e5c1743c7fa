<?php
// Script pour ajouter des données de test
include_once 'config/database.php';

echo "<h1>Ajout de données de test</h1>";

$database = new Database();
$db = $database->getConnection();

// Vérifier s'il y a déjà des patients
$query = "SELECT COUNT(*) as total FROM patients";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result['total'] == 0) {
    echo "<h2>Ajout de patients de test</h2>";
    
    // Ajouter quelques patients de test
    $patients = [
        ['nom' => 'Dupont', 'prenom' => 'Jean', 'telephone' => '**********'],
        ['nom' => 'Martin', 'prenom' => 'Marie', 'telephone' => '**********'],
        ['nom' => '<PERSON>', 'prenom' => '<PERSON>', 'telephone' => '**********'],
        ['nom' => 'Durand', 'prenom' => 'Sophie', 'telephone' => '**********']
    ];
    
    foreach ($patients as $patient) {
        $query = "INSERT INTO patients (nom, prenom, telephone) VALUES (?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->execute([$patient['nom'], $patient['prenom'], $patient['telephone']]);
        echo "Patient ajouté: {$patient['nom']} {$patient['prenom']}<br>";
    }
}

// Récupérer les IDs des patients
$query = "SELECT id, nom, prenom FROM patients LIMIT 4";
$stmt = $db->prepare($query);
$stmt->execute();
$patients = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($patients) > 0) {
    echo "<h2>Ajout de rendez-vous de test</h2>";
    
    // Supprimer les anciens rendez-vous de test
    $query = "DELETE FROM visites WHERE notes LIKE '%TEST%'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    // Ajouter des rendez-vous pour cette semaine
    $today = new DateTime();
    $startOfWeek = clone $today;
    $startOfWeek->modify('monday this week');
    
    $rendezVous = [];
    
    // Lundi
    $date = clone $startOfWeek;
    $rendezVous[] = [
        'patient_id' => $patients[0]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '09:00',
        'type' => 'cabinet',
        'status' => 'rendez-vous',
        'notes' => 'TEST - Consultation de routine'
    ];
    
    $rendezVous[] = [
        'patient_id' => $patients[1]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '10:30',
        'type' => 'cabinet',
        'status' => 'fait',
        'notes' => 'TEST - Suivi médical'
    ];
    
    // Mardi
    $date->modify('+1 day');
    $rendezVous[] = [
        'patient_id' => $patients[2]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '14:00',
        'type' => 'domicile',
        'status' => 'present',
        'notes' => 'TEST - Visite à domicile'
    ];
    
    // Mercredi
    $date->modify('+1 day');
    $rendezVous[] = [
        'patient_id' => $patients[3]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '11:00',
        'type' => 'controle',
        'status' => 'rendez-vous',
        'notes' => 'TEST - Contrôle post-opératoire'
    ];
    
    // Jeudi
    $date->modify('+1 day');
    $rendezVous[] = [
        'patient_id' => $patients[0]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '16:00',
        'type' => 'cabinet',
        'status' => 'rendez-vous',
        'notes' => 'TEST - Consultation spécialisée'
    ];
    
    // Vendredi
    $date->modify('+1 day');
    $rendezVous[] = [
        'patient_id' => $patients[1]['id'],
        'date' => $date->format('Y-m-d'),
        'heure' => '08:30',
        'type' => 'cabinet',
        'status' => 'fait',
        'notes' => 'TEST - Bilan de santé'
    ];
    
    // Insérer les rendez-vous
    foreach ($rendezVous as $rdv) {
        $query = "INSERT INTO visites (patient_id, date_visite, heure_visite, type_visite, status, notes) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($query);
        $stmt->execute([
            $rdv['patient_id'],
            $rdv['date'],
            $rdv['heure'],
            $rdv['type'],
            $rdv['status'],
            $rdv['notes']
        ]);
        
        // Récupérer le nom du patient
        $patient = array_filter($patients, function($p) use ($rdv) {
            return $p['id'] == $rdv['patient_id'];
        });
        $patient = array_values($patient)[0];
        
        echo "Rendez-vous ajouté: {$patient['nom']} {$patient['prenom']} - {$rdv['date']} {$rdv['heure']} ({$rdv['status']})<br>";
    }
    
    echo "<br><strong>✅ Données de test ajoutées avec succès !</strong><br>";
    echo "<a href='consultations.php'>Aller aux consultations</a><br>";
    echo "<a href='test_api.php'>Tester l'API</a><br>";
    
} else {
    echo "❌ Aucun patient trouvé pour créer des rendez-vous<br>";
}
?>
