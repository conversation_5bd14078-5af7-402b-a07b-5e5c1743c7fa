# Test de la requête PUT pour modifier une visite
$uri = "http://localhost/patients/api/visites.php?id=11"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

$body = @{
    patient_id = 1
    date_visite = "2025-05-31"
    heure_visite = "13:39"
    type_visite = "cabinet"
    status = "fait"
    honoraire = ""
    motif = "reter"
    histoire_maladie = "ert"
    examens_cliniques = "ert"
    examens_complementaires = "ert"
    diagnostic = "ert"
    cat = "ert"
} | ConvertTo-Json

Write-Host "URI: $uri"
Write-Host "Body: $body"

try {
    $response = Invoke-WebRequest -Uri $uri -Method PUT -Headers $headers -Body $body
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Response: $($response.Content)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error Response: $responseBody"
    }
}
