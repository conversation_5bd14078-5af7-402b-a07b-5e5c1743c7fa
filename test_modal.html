<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Rendez-vous</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test du Modal Nouveau Rendez-vous</h1>
        <p>Cette page permet de tester les corrections apportées au formulaire de création de rendez-vous.</p>
        
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newVisitModal">
            <i class="fas fa-plus me-2"></i>Nouveau rendez-vous
        </button>

        <div class="mt-4">
            <h3>Tests à effectuer :</h3>
            <ul>
                <li>✅ Cliquer rapidement plusieurs fois sur "Enregistrer" ne doit créer qu'un seul rendez-vous</li>
                <li>✅ Le bouton doit se désactiver pendant l'envoi</li>
                <li>✅ Un spinner doit apparaître pendant l'envoi</li>
                <li>✅ Le bouton doit se réactiver après la réponse</li>
                <li>✅ Les validations doivent fonctionner</li>
            </ul>
        </div>
    </div>

    <!-- Modal Nouveau rendez-vous -->
    <div class="modal fade" id="newVisitModal" tabindex="-1" aria-labelledby="newVisitModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newVisitModalLabel">Nouveau rendez-vous</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newVisitForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="patient" class="form-label">Patient</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="patient" required autocomplete="off" spellcheck="false">
                                        <input type="hidden" id="patient_id" value="1">
                                        <button class="btn btn-outline-secondary" type="button" id="createPatientBtn">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type_visite" class="form-label">Type de consultation</label>
                                    <select class="form-select" id="type_visite" required>
                                        <option value="">Sélectionner...</option>
                                        <option value="consultation">Consultation</option>
                                        <option value="controle">Contrôle</option>
                                        <option value="urgence">Urgence</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_visite" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="date_visite" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="heure_visite" class="form-label">Heure</label>
                                    <select class="form-select" id="heure_visite" required>
                                        <option value="09:00">09:00</option>
                                        <option value="09:30">09:30</option>
                                        <option value="10:00">10:00</option>
                                        <option value="10:30">10:30</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="saveVisit">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Fonction toast pour les tests
        function showToast(message, type = 'success') {
            alert(`${type.toUpperCase()}: ${message}`);
        }

        // Simuler le comportement du dashboard.js
        $(document).ready(function() {
            // Sauvegarder un nouveau rendez-vous
            $('#saveVisit').off('click').on('click', function() {
                const $button = $(this);
                
                // Vérifier si une requête est déjà en cours
                if ($button.prop('disabled')) {
                    return;
                }
                
                const visitData = {
                    patient_id: $('#patient_id').val(),
                    date_visite: $('#date_visite').val(),
                    heure_visite: $('#heure_visite').val(),
                    type_visite: $('#type_visite').val(),
                    notes: $('#notes').val(),
                    status: 'rendez-vous'
                };

                if (!visitData.patient_id) {
                    showToast('Veuillez sélectionner ou créer un patient', 'error');
                    return;
                }

                if (!visitData.type_visite) {
                    showToast('Veuillez sélectionner le type de consultation', 'error');
                    return;
                }

                // Désactiver le bouton et changer le texte
                $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...');

                // Simuler une requête AJAX avec délai
                setTimeout(function() {
                    showToast('Rendez-vous créé avec succès (simulation)');
                    $('#newVisitModal').modal('hide');
                }, 2000); // 2 secondes pour simuler la requête
            });

            // Réinitialiser l'état du bouton quand le modal s'ouvre
            $('#newVisitModal').on('show.bs.modal', function() {
                const $saveButton = $('#saveVisit');
                $saveButton.prop('disabled', false).html('Enregistrer');
                
                // Réinitialiser la date à aujourd'hui si elle n'est pas définie
                const $dateInput = $('#date_visite');
                if (!$dateInput.val()) {
                    $dateInput.val(new Date().toISOString().split('T')[0]);
                }
                
                // Valeurs par défaut pour le test
                $('#patient').val('Test Patient');
                $('#patient_id').val('1');
            });

            // Fonction pour réinitialiser complètement le formulaire
            function resetNewVisitForm() {
                // Réinitialiser le formulaire
                $('#newVisitForm')[0].reset();

                // Vider tous les champs spécifiques
                $('#patient').val('');
                $('#patient_id').val('');
                $('#date_visite').val('');
                $('#heure_visite').val('');
                $('#type_visite').val('');
                $('#notes').val('');

                // Réinitialiser le bouton
                const $saveButton = $('#saveVisit');
                $saveButton.prop('disabled', false).html('Enregistrer');
            }

            // Nettoyer le formulaire à chaque fermeture du modal
            $('#newVisitModal').on('hidden.bs.modal', function() {
                resetNewVisitForm();
            });
        });
    </script>
</body>
</html>
