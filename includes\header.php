<?php
require_once 'includes/auth.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cabinet Médical - Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 70px;
            --primary-color: #2563eb;
            --secondary-color: #f8f9fa;
            --text-color: #1e293b;
            --hover-color: #eff6ff;
            --border-color: #e2e8f0;
        }

        body {
            font-family: 'Inter', 'Segoe UI', sans-serif;
            background-color: #f8fafc;
            color: var(--text-color);
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: white;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-header .logo {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
            text-decoration: none;
            white-space: nowrap;
        }

        .sidebar .nav-link {
            color: var(--text-color);
            padding: 0.875rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.2s ease;
            border-radius: 0.5rem;
            margin: 0.25rem 0.75rem;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            background-color: var(--hover-color);
            color: var(--primary-color);
        }

        .sidebar .nav-link.active {
            background-color: var(--hover-color);
            color: var(--primary-color);
        }

        .sidebar .nav-link i {
            font-size: 1.25rem;
            width: 1.5rem;
            text-align: center;
        }

        .sidebar.collapsed .nav-link span,
        .sidebar.collapsed .logo-text {
            display: none;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-left: var(--sidebar-collapsed-width);
        }

        .navbar {
            display: none;
            background: white !important;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        }

        .navbar .nav-link {
            color: var(--text-color) !important;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .navbar .nav-link:hover {
            background-color: var(--hover-color);
            color: var(--primary-color) !important;
        }

        #sidebarToggle {
            position: fixed;
            left: calc(var(--sidebar-width) - 15px);
            top: 1.5rem;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            transition: all 0.3s ease;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--text-color);
        }

        #sidebarToggle:hover {
            background-color: var(--hover-color);
            color: var(--primary-color);
        }

        #sidebarToggle.collapsed {
            left: calc(var(--sidebar-collapsed-width) - 15px);
        }

        .nav-section {
            margin-top: 1rem;
            padding: 0.5rem 0;
        }

        .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: #64748b;
            padding: 0.5rem 1.5rem;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                max-width: 280px;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
                padding-top: 4.5rem;
            }

            .navbar {
                display: flex;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 900;
            }

            #sidebarToggle {
                display: none;
            }

            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
            }

            .overlay.show {
                display: block;
            }
        }

        /* Styles personnalisés pour FullCalendar */
        .fc-event {
            cursor: pointer;
            border-radius: 4px;
            font-size: 0.85rem;
            padding: 2px 4px;
        }

        .fc-event:hover {
            opacity: 0.8;
        }

        .fc-timegrid-slot {
            height: 40px;
        }

        .fc-col-header-cell {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .fc-timegrid-axis {
            background-color: #f8f9fa;
        }

        #calendar-container {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navbar mobile -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <i class="fas fa-clinic-medical me-2 text-primary"></i>
                <span>Cabinet Médical</span>
            </a>
            <button class="navbar-toggler border-0" type="button" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Overlay pour mobile -->
    <div class="overlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="index.php" class="logo">
                <i class="fas fa-clinic-medical me-2"></i>
                <span class="logo-text">Cabinet Médical</span>
            </a>
        </div>
        
        <div class="nav-section">
            <div class="nav-section-title">Menu Principal</div>
            <div class="nav flex-column">
                <a href="index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                    <i class="fas fa-home"></i>
                    <span>Tableau de bord</span>
                </a>
                <a href="patients.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'patients.php' ? 'active' : ''; ?>">
                    <i class="fas fa-users"></i>
                    <span>Patients</span>
                </a>
                <a href="consultations.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'consultations.php' ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-check"></i>
                    <span>Consultations</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Administration</div>
            <div class="nav flex-column">
                <?php if (is_admin()): ?>
                <a href="#" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Paramètres</span>
                </a>
                <?php endif; ?>
                <div class="nav-link text-muted">
                    <i class="fas fa-user"></i>
                    <span><?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                </div>
                <a href="logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Déconnexion</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Bouton toggle sidebar -->
    <button id="sidebarToggle">
        <i class="fas fa-chevron-left"></i>
    </button>

    <!-- Contenu principal -->
    <div class="main-content">

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const toggleIcon = sidebarToggle.querySelector('i');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const overlay = document.querySelector('.overlay');

    // Gérer le toggle du menu desktop
    sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        sidebarToggle.classList.toggle('collapsed');
        
        if (sidebar.classList.contains('collapsed')) {
            toggleIcon.classList.remove('fa-chevron-left');
            toggleIcon.classList.add('fa-chevron-right');
        } else {
            toggleIcon.classList.remove('fa-chevron-right');
            toggleIcon.classList.add('fa-chevron-left');
        }
    });

    // Gérer le menu mobile
    function toggleMobileMenu() {
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
        document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
    }

    mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    overlay.addEventListener('click', toggleMobileMenu);

    // Fermer le menu mobile sur les liens
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                toggleMobileMenu();
            }
        });
    });

    // Gérer le redimensionnement
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});
</script> 