<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

if(isset($_GET['id'])) {
    $patient_id = $_GET['id'];
    $response = array();

    // Récupérer les informations du patient
    $query = "SELECT * FROM patients WHERE id = ?";
    $stmt = $db->prepare($query);
    $stmt->execute([$patient_id]);
    $response['patient'] = $stmt->fetch(PDO::FETCH_ASSOC);

    // Récupérer les antécédents
    $query = "SELECT * FROM antecedents WHERE patient_id = ? ORDER BY date_ajout DESC";
    $stmt = $db->prepare($query);
    $stmt->execute([$patient_id]);
    $response['antecedents'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Récupérer les traitements
    $query = "SELECT * FROM traitements WHERE patient_id = ? ORDER BY date_creation DESC";
    $stmt = $db->prepare($query);
    $stmt->execute([$patient_id]);
    $response['traitements'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($response);
} else {
    http_response_code(400);
    echo json_encode(array("message" => "ID du patient non spécifié"));
} 