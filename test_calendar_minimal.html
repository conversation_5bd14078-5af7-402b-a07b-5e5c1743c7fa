<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Calendrier Minimal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Calendrier Minimal</h1>
        <div id="calendar"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/locales/fr.global.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test calendrier minimal');
            
            const calendarEl = document.getElementById('calendar');
            
            // Événements statiques pour test
            const events = [
                {
                    id: '1',
                    title: 'Test Event 1',
                    start: '2025-05-27T09:00:00',
                    end: '2025-05-27T09:30:00',
                    backgroundColor: '#fbbf24'
                },
                {
                    id: '2',
                    title: 'Test Event 2',
                    start: '2025-05-27T14:00:00',
                    end: '2025-05-27T14:30:00',
                    backgroundColor: '#60a5fa'
                },
                {
                    id: '3',
                    title: 'Test Event 3',
                    start: '2025-05-28T10:00:00',
                    end: '2025-05-28T10:30:00',
                    backgroundColor: '#34d399'
                }
            ];
            
            console.log('Événements de test:', events);
            
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                locale: 'fr',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridWeek,timeGridDay'
                },
                slotMinTime: '08:00:00',
                slotMaxTime: '19:00:00',
                slotDuration: '00:30:00',
                height: 'auto',
                allDaySlot: false,
                weekends: false,
                events: events,
                eventDidMount: function(info) {
                    console.log('Événement monté:', info.event.title);
                }
            });
            
            console.log('Rendu du calendrier...');
            calendar.render();
            console.log('Calendrier rendu');
        });
    </script>
</body>
</html>
