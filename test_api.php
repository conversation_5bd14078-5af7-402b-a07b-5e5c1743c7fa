<?php
// Test simple pour vérifier l'API des visites
include_once 'config/database.php';

echo "<h1>Test API Visites</h1>";

$database = new Database();
$db = $database->getConnection();

// Test 1: Vérifier la connexion à la base de données
echo "<h2>1. Test de connexion à la base de données</h2>";
if ($db) {
    echo "✅ Connexion réussie<br>";
} else {
    echo "❌ Échec de la connexion<br>";
    exit;
}

// Test 2: Vérifier la structure de la table visites
echo "<h2>2. Structure de la table visites</h2>";
try {
    $query = "DESCRIBE visites";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
}

// Test 3: Compter le nombre de visites
echo "<h2>3. Nombre total de visites</h2>";
try {
    $query = "SELECT COUNT(*) as total FROM visites";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Nombre total de visites: " . $result['total'] . "<br>";
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
}

// Test 4: Afficher quelques visites avec les patients
echo "<h2>4. Quelques visites avec patients</h2>";
try {
    $query = "SELECT v.*, p.nom as nom_patient, p.prenom as prenom_patient 
             FROM visites v 
             JOIN patients p ON v.patient_id = p.id 
             ORDER BY v.date_visite DESC, v.heure_visite ASC 
             LIMIT 5";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $visites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($visites) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Patient</th><th>Date</th><th>Heure</th><th>Type</th><th>Statut</th></tr>";
        foreach ($visites as $visite) {
            echo "<tr>";
            echo "<td>{$visite['id']}</td>";
            echo "<td>{$visite['nom_patient']} {$visite['prenom_patient']}</td>";
            echo "<td>{$visite['date_visite']}</td>";
            echo "<td>{$visite['heure_visite']}</td>";
            echo "<td>{$visite['type_visite']}</td>";
            echo "<td>{$visite['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Aucune visite trouvée.<br>";
    }
} catch (PDOException $e) {
    echo "❌ Erreur: " . $e->getMessage() . "<br>";
}

// Test 5: Test de l'API via cURL
echo "<h2>5. Test de l'API via cURL</h2>";
$url = 'http://localhost/patients/api/visites.php';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, false);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Code HTTP: " . $httpCode . "<br>";
echo "Réponse: <pre>" . htmlspecialchars($response) . "</pre>";

// Test 6: Décoder la réponse JSON
if ($httpCode == 200) {
    $data = json_decode($response, true);
    if ($data !== null) {
        echo "<h3>Données décodées:</h3>";
        echo "Nombre d'éléments: " . count($data) . "<br>";
        if (count($data) > 0) {
            echo "Premier élément: <pre>" . print_r($data[0], true) . "</pre>";
        }
    } else {
        echo "❌ Erreur de décodage JSON<br>";
    }
}
?>
