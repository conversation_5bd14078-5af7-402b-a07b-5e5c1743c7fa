<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

// Lire le fichier des médicaments
$medicaments = file('../medicaments.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

// Si un terme de recherche est fourni
if(isset($_GET['term'])) {
    $term = strtolower($_GET['term']);
    
    // Filtrer les médicaments qui correspondent au terme
    $suggestions = array_filter($medicaments, function($medicament) use ($term) {
        return strpos(strtolower($medicament), $term) !== false;
    });
    
    // Limiter à 10 suggestions
    $suggestions = array_slice(array_values($suggestions), 0, 10);
    
    echo json_encode($suggestions);
} else {
    // Si aucun terme n'est fourni, renvoyer une liste vide
    echo json_encode([]);
} 