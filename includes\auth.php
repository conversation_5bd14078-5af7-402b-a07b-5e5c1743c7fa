<?php
session_start();

// Vérifier si l'utilisateur n'est pas connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Fonction pour vérifier si l'utilisateur est admin
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Fonction pour vérifier si l'utilisateur a accès à une page
function check_access($required_role = null) {
    if ($required_role === 'admin' && !is_admin()) {
        header('Location: index.php');
        exit();
    }
}
?> 