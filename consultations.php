<?php
$page_title = "Consultations";
include 'includes/header.php';
?>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* Variables CSS pour la cohérence des couleurs */
    :root {
        --primary-color: #2563eb;
        --primary-light: #3b82f6;
        --primary-dark: #1d4ed8;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #06b6d4;
        --light-bg: #f8fafc;
        --border-color: #e2e8f0;
        --text-muted: #64748b;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    }

    /* Style général de la page */
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Header amélioré */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--shadow-lg);
    }

    .page-header h1 {
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
    }

    .page-header .breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin-top: 1rem;
    }

    .page-header .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
    }

    .page-header .breadcrumb-item.active {
        color: white;
    }

    /* Cards améliorées */
    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        background: white;
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }

    /* Onglets améliorés */
    .nav-tabs {
        border: none;
        gap: 0.5rem;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        color: var(--text-muted);
        background: rgba(255, 255, 255, 0.7);
        transition: all 0.3s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(37, 99, 235, 0.1);
        color: var(--primary-color);
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link.active {
        background: var(--primary-color);
        color: white;
        box-shadow: var(--shadow-md);
    }

    .nav-tabs .nav-link i {
        font-size: 1.1rem;
    }

    /* Tableaux améliorés */
    .table {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
        border: none;
        padding: 1rem;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: var(--light-bg);
        transform: scale(1.01);
    }

    .table tbody td {
        padding: 1rem;
        border-color: var(--border-color);
        vertical-align: middle;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.75rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    }

    .btn-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Badges de statut améliorés */
    .badge {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .bg-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%) !important;
    }

    .bg-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%) !important;
    }

    /* Légende améliorée */
    .legend-container {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 0.75rem;
        padding: 1rem;
        border: 2px solid var(--border-color);
        margin-bottom: 1.5rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
    }

    .legend-color {
        width: 24px;
        height: 16px;
        border-radius: 0.25rem;
        border: 2px solid rgba(0, 0, 0, 0.1);
    }

    /* Améliorations des modals */
    .modal-content {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-lg);
    }

    .modal-header {
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 2px solid var(--border-color);
        border-radius: 0 0 1rem 1rem;
        padding: 1.5rem;
    }

    /* Améliorations des formulaires */
    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-muted);
        margin-bottom: 0.5rem;
    }

    /* Animation d'entrée pour les cartes */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: slideInUp 0.6s ease-out;
    }

    /* Amélioration des toasts */
    .toast {
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .nav-tabs .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .legend-container {
            padding: 0.75rem;
        }
    }
</style>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- Header de la page amélioré -->
<div class="page-header">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1>
                    <i class="fas fa-calendar-check me-3"></i>
                    Gestion des Consultations
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="dashboard.php">
                                <i class="fas fa-home"></i> Accueil
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-calendar-check"></i> Consultations
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#newVisitModal">
                    <i class="fas fa-plus"></i> Nouveau rendez-vous
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-4">

    <!-- Modal Nouveau rendez-vous -->
    <div class="modal fade" id="newVisitModal" tabindex="-1" aria-labelledby="newVisitModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="newVisitModalLabel">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Nouveau rendez-vous
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newVisitForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="patient" class="form-label">Patient</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="patient" required autocomplete="off" spellcheck="false">
                                        <input type="hidden" id="patient_id">
                                        <button class="btn btn-outline-secondary" type="button" id="createPatientBtn">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                    </div>
                                    <div id="patientsList" class="list-group position-absolute w-100 d-none" style="z-index: 1000;"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type_visite" class="form-label">Type de consultation</label>
                                    <select class="form-select" id="type_visite" required>
                                        <option value="cabinet" selected>Cabinet</option>
                                        <option value="domicile">Domicile</option>
                                        <option value="controle">Contrôle</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_visite" class="form-label">Date</label>
                                    <input type="date" class="form-control" id="date_visite" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="heure_visite" class="form-label">Heure</label>
                                    <select class="form-select" id="heure_visite" required>
                                        <option value="">Choisir une heure</option>
                                        <option value="08:00">08:00</option>
                                        <option value="08:30">08:30</option>
                                        <option value="09:00">09:00</option>
                                        <option value="09:30">09:30</option>
                                        <option value="10:00">10:00</option>
                                        <option value="10:30">10:30</option>
                                        <option value="11:00">11:00</option>
                                        <option value="11:30">11:30</option>
                                        <option value="12:00">12:00</option>
                                        <option value="12:30">12:30</option>
                                        <option value="14:00">14:00</option>
                                        <option value="14:30">14:30</option>
                                        <option value="15:00">15:00</option>
                                        <option value="15:30">15:30</option>
                                        <option value="16:00">16:00</option>
                                        <option value="16:30">16:30</option>
                                        <option value="17:00">17:00</option>
                                        <option value="17:30">17:30</option>
                                        <option value="18:00">18:00</option>
                                        <option value="18:30">18:30</option>
                                        <option value="19:00">19:00</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="saveVisit">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nouveau Patient -->
    <div class="modal fade" id="newPatientModal" tabindex="-1" aria-labelledby="newPatientModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="newPatientModalLabel">
                        <i class="fas fa-user-plus me-2"></i>
                        Nouveau patient
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newPatientForm">
                        <div class="mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" required>
                        </div>
                        <div class="mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" required>
                        </div>
                        <div class="mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="telephone">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="savePatient">Enregistrer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Card avec les onglets -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#today">
                        <i class="fas fa-calendar-day"></i> Consultations d'aujourd'hui
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#calendar">
                        <i class="fas fa-calendar-week"></i> Calendrier hebdomadaire
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#all">
                        <i class="fas fa-calendar-alt"></i> Toutes les consultations
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <!-- Onglet Consultations d'aujourd'hui -->
                <div class="tab-pane fade show active" id="today">
                    <div class="card border-0">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-day text-primary me-2"></i>
                                Consultations du jour
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="todayConsultationsTable">
                                    <thead>
                                        <tr>
                                            <th>Heure</th>
                                            <th>Patient</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Les consultations seront chargées ici -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Calendrier hebdomadaire -->
                <div class="tab-pane fade" id="calendar">
                    <!-- Légende des couleurs améliorée -->
                    <div class="legend-container">
                        <div class="d-flex flex-wrap gap-3 align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-palette text-primary me-2"></i>
                                <span class="fw-bold">Légende :</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);"></div>
                                <span>Rendez-vous</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);"></div>
                                <span>Présent</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: linear-gradient(135deg, #34d399 0%, #10b981 100%);"></div>
                                <span>Fait</span>
                            </div>
                            <div class="ms-auto d-flex align-items-center gap-2">
                                <button id="refreshCalendar" class="btn btn-sm btn-primary">
                                    <i class="fas fa-sync-alt"></i> Actualiser
                                </button>
                                <div class="d-flex align-items-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>Cliquez sur un créneau pour créer un rendez-vous</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="calendar-container">
                        <!-- Le calendrier sera rendu ici -->
                    </div>
                </div>

                <!-- Onglet Toutes les consultations -->
                <div class="tab-pane fade" id="all">
                    <div class="card border-0">
                        <div class="card-header bg-transparent border-0">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt text-primary me-2"></i>
                                Historique complet des consultations
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="allConsultationsTable">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Heure</th>
                                            <th>Patient</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Les consultations seront chargées ici -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
$(document).ready(function() {
    // Définir la date d'aujourd'hui par défaut
    const today = new Date().toISOString().split('T')[0];
    $('#date_visite').val(today);

    // Configuration française pour DataTables
    const frenchTranslation = {
        "emptyTable": "Aucune donnée disponible dans le tableau",
        "info": "Affichage de _START_ à _END_ sur _TOTAL_ entrées",
        "infoEmpty": "Affichage de 0 à 0 sur 0 entrée",
        "infoFiltered": "(filtré à partir de _MAX_ entrées au total)",
        "infoThousands": " ",
        "lengthMenu": "Afficher _MENU_ entrées",
        "loadingRecords": "Chargement...",
        "processing": "Traitement...",
        "search": "Rechercher :",
        "zeroRecords": "Aucun élément correspondant trouvé",
        "paginate": {
            "first": "Premier",
            "last": "Dernier",
            "next": "Suivant",
            "previous": "Précédent"
        },
        "aria": {
            "sortAscending": ": activer pour trier la colonne par ordre croissant",
            "sortDescending": ": activer pour trier la colonne par ordre décroissant"
        }
    };

    // Fonction pour formater la date
    function formatDate(date) {
        return new Date(date).toLocaleDateString('fr-FR');
    }

    // Fonction pour obtenir la couleur du badge selon le status
    function getBadgeColor(status) {
        switch(status) {
            case 'rendez-vous': return 'warning';
            case 'present': return 'info';
            case 'fait': return 'success';
            default: return 'secondary';
        }
    }

    // Variables pour stocker les instances de DataTable
    let todayTable, allTable;

    // Fonction pour charger les consultations d'aujourd'hui
    function loadTodayConsultations() {
        console.log('Chargement des consultations d\'aujourd\'hui...');
        $.get('api/visites.php?today=1', function(data) {
            console.log('Consultations d\'aujourd\'hui reçues:', data);
            const consultationsList = data.map(visite => `
                <tr>
                    <td>${visite.heure_visite}</td>
                    <td>
                        <a href="patient_details.php?id=${visite.patient_id}">
                            ${visite.nom_patient} ${visite.prenom_patient}
                        </a>
                    </td>
                    <td>${visite.type_visite}</td>
                    <td>
                        <span class="badge bg-${getBadgeColor(visite.status)}">
                            ${visite.status}
                        </span>
                    </td>
                    <td>
                        ${visite.status === 'rendez-vous' ? `
                            <button class="btn btn-sm btn-success mark-present" data-id="${visite.id}">
                                <i class="fas fa-check"></i> Présent
                            </button>
                        ` : '-'}
                    </td>
                </tr>
            `).join('');
            
            // Détruire la table existante si elle existe
            if (todayTable) {
                todayTable.destroy();
            }
            
            // Mettre à jour le contenu
            $('#todayConsultationsTable tbody').html(consultationsList);
            console.log('Table aujourd\'hui mise à jour');
            
            // Réinitialiser DataTable
            todayTable = $('#todayConsultationsTable').DataTable({
                language: frenchTranslation,
                order: [[0, 'asc']],
                pageLength: 25,
                responsive: true
            });
        }).fail(function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement des consultations d\'aujourd\'hui:', {
                status: textStatus,
                error: errorThrown,
                response: jqXHR.responseText
            });
            showToast('Erreur lors du chargement des consultations', 'error');
        });
    }

    // Fonction pour charger toutes les consultations
    function loadAllConsultations() {
        console.log('Chargement de toutes les consultations...');
        $.get('api/visites.php', function(data) {
            console.log('Toutes les consultations reçues:', data);
            const consultationsList = data.map(visite => `
                <tr>
                    <td>${formatDate(visite.date_visite)}</td>
                    <td>${visite.heure_visite}</td>
                    <td>
                        <a href="patient_details.php?id=${visite.patient_id}">
                            ${visite.nom_patient} ${visite.prenom_patient}
                        </a>
                    </td>
                    <td>${visite.type_visite}</td>
                    <td>
                        <span class="badge bg-${getBadgeColor(visite.status)}">
                            ${visite.status}
                        </span>
                    </td>
                    <td>
                        ${visite.status === 'rendez-vous' ? `
                            <button class="btn btn-sm btn-success mark-present" data-id="${visite.id}">
                                <i class="fas fa-check"></i> Présent
                            </button>
                        ` : '-'}
                    </td>
                </tr>
            `).join('');
            
            // Détruire la table existante si elle existe
            if (allTable) {
                allTable.destroy();
            }
            
            // Mettre à jour le contenu
            $('#allConsultationsTable tbody').html(consultationsList);
            console.log('Table complète mise à jour');
            
            // Réinitialiser DataTable
            allTable = $('#allConsultationsTable').DataTable({
                language: frenchTranslation,
                order: [[0, 'desc'], [1, 'asc']],
                pageLength: 25,
                responsive: true
            });
        }).fail(function(jqXHR, textStatus, errorThrown) {
            console.error('Erreur lors du chargement de toutes les consultations:', {
                status: textStatus,
                error: errorThrown,
                response: jqXHR.responseText
            });
            showToast('Erreur lors du chargement des consultations', 'error');
        });
    }

    // Charger les données initiales
    loadTodayConsultations();
    loadAllConsultations();

    // Initialiser le calendrier si l'onglet calendrier est actif au chargement
    if ($('#calendar').hasClass('show') && $('#calendar').hasClass('active')) {
        console.log('Onglet calendrier actif au chargement, initialisation...');
        initCalendar();
    }

    // Variable pour stocker l'instance du calendrier
    let calendar;

    // Fonction pour charger les événements du calendrier
    function loadCalendarEvents() {
        console.log('Chargement des événements du calendrier...');

        // Test avec un événement statique d'abord
        const testEvent = {
            id: 'test',
            title: 'Test Event',
            start: '2025-05-27T10:00:00',
            end: '2025-05-27T10:30:00',
            backgroundColor: '#fbbf24',
            borderColor: '#f59e0b'
        };
        console.log('Événement de test créé:', testEvent);

        return $.get('api/visites.php').then(function(data) {
            console.log('Données reçues pour le calendrier:', data);

            if (!data || data.length === 0) {
                console.log('Aucune donnée reçue pour le calendrier, retour de l\'événement de test');
                return [testEvent];
            }

            const events = data.map(visite => {
                console.log('Traitement de la visite:', visite);

                // Vérifier et nettoyer les données
                if (!visite.date_visite || !visite.heure_visite) {
                    console.warn('Données manquantes pour la visite:', visite);
                    return null;
                }

                // Construire la date/heure de début
                let startDateTime;
                try {
                    // S'assurer que l'heure a le bon format (HH:MM)
                    let heure = visite.heure_visite;
                    if (heure.length === 5) { // Format HH:MM
                        startDateTime = `${visite.date_visite}T${heure}:00`;
                    } else if (heure.length === 8) { // Format HH:MM:SS
                        startDateTime = `${visite.date_visite}T${heure}`;
                    } else {
                        console.warn('Format d\'heure invalide:', heure);
                        return null;
                    }

                    console.log('DateTime créé:', startDateTime);
                } catch (error) {
                    console.error('Erreur lors de la création de la date:', error);
                    return null;
                }

                // Calculer la fin (30 minutes après le début)
                const startDate = new Date(startDateTime);
                if (isNaN(startDate.getTime())) {
                    console.error('Date invalide:', startDateTime);
                    return null;
                }

                const endDate = new Date(startDate.getTime() + 30 * 60000); // 30 minutes

                // Couleurs selon le statut
                let backgroundColor, borderColor;
                switch(visite.status) {
                    case 'rendez-vous':
                        backgroundColor = '#fbbf24'; // jaune
                        borderColor = '#f59e0b';
                        break;
                    case 'present':
                        backgroundColor = '#60a5fa'; // bleu
                        borderColor = '#3b82f6';
                        break;
                    case 'fait':
                        backgroundColor = '#34d399'; // vert
                        borderColor = '#10b981';
                        break;
                    default:
                        backgroundColor = '#9ca3af'; // gris
                        borderColor = '#6b7280';
                }

                const event = {
                    id: visite.id,
                    title: `${visite.nom_patient} ${visite.prenom_patient}`,
                    start: startDateTime,
                    end: endDate.toISOString(),
                    backgroundColor: backgroundColor,
                    borderColor: borderColor,
                    extendedProps: {
                        patient_id: visite.patient_id,
                        type_visite: visite.type_visite,
                        status: visite.status,
                        notes: visite.notes
                    }
                };

                console.log('Événement créé:', event);
                return event;
            }).filter(event => event !== null); // Filtrer les événements null

            console.log('Tous les événements créés (après filtrage):', events);
            console.log('Nombre d\'événements valides:', events.length);

            // Ajouter l'événement de test
            events.push(testEvent);
            console.log('Événements finaux avec test:', events);

            return events;
        }).catch(function(error) {
            console.error('Erreur lors du chargement des événements:', error);
            return [];
        });
    }

    // Initialiser le calendrier
    function initCalendar() {
        console.log('Initialisation du calendrier...');
        const calendarEl = document.getElementById('calendar-container');

        if (!calendarEl) {
            console.error('Élément calendar-container non trouvé');
            return;
        }

        console.log('Élément calendrier trouvé:', calendarEl);

        // Charger les événements d'abord, puis initialiser le calendrier
        loadCalendarEvents().then(function(events) {
            console.log('Événements chargés, initialisation du calendrier avec:', events);

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                locale: 'fr',
                firstDay: 1, // Commencer la semaine par lundi (1 = lundi, 0 = dimanche)
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridWeek,timeGridDay'
                },
                slotMinTime: '08:00:00',
                slotMaxTime: '19:00:00',
                slotDuration: '00:30:00',
                height: 'auto',
                allDaySlot: false,
                weekends: true,
                businessHours: [
                    {
                        daysOfWeek: [1, 2, 3, 4, 5, 6], // Lundi à Samedi - horaires normaux
                        startTime: '08:00',
                        endTime: '19:00'
                    },
                    {
                        daysOfWeek: [0], // Dimanche - horaires réduits (optionnel)
                        startTime: '09:00',
                        endTime: '17:00'
                    }
                ],
                events: events, // Utiliser les événements pré-chargés
                eventClick: function(info) {
                    // Rediriger vers la page du patient
                    if (info.event.extendedProps && info.event.extendedProps.patient_id) {
                        window.location.href = `patient_details.php?id=${info.event.extendedProps.patient_id}`;
                    }
                },
                eventDidMount: function(info) {
                    console.log('Événement monté dans le calendrier:', info.event.title);
                    // Ajouter une tooltip avec les détails
                    if (info.event.extendedProps) {
                        const tooltip = `
                            Patient: ${info.event.title}
                            Type: ${info.event.extendedProps.type_visite || 'N/A'}
                            Statut: ${info.event.extendedProps.status || 'N/A'}
                            ${info.event.extendedProps.notes ? 'Notes: ' + info.event.extendedProps.notes : ''}
                        `;
                        info.el.setAttribute('title', tooltip);
                    }
                },
                dateClick: function(info) {
                    // Ouvrir le modal de nouveau rendez-vous avec la date/heure pré-remplie
                    const clickedDate = info.date;
                    const dateStr = clickedDate.toISOString().split('T')[0];
                    const timeStr = clickedDate.toTimeString().slice(0, 5);

                    $('#date_visite').val(dateStr);
                    $('#heure_visite').val(timeStr);
                    $('#newVisitModal').modal('show');
                }
            });

            console.log('Rendu du calendrier...');
            calendar.render();
            console.log('Calendrier rendu avec succès');

        }).catch(function(error) {
            console.error('Erreur lors du chargement des événements pour l\'initialisation:', error);
        });
    }

    // Vérifier si FullCalendar est chargé
    if (typeof FullCalendar === 'undefined') {
        console.error('FullCalendar n\'est pas chargé !');
        showToast('Erreur: FullCalendar non chargé', 'error');
    } else {
        console.log('FullCalendar est chargé correctement');
    }

    // Recharger les données lors du changement d'onglet
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        const target = $(e.target).attr('href');
        console.log('Changement d\'onglet vers:', target);

        if (target === '#today') {
            loadTodayConsultations();
        } else if (target === '#calendar') {
            console.log('Activation de l\'onglet calendrier');
            if (!calendar) {
                console.log('Calendrier non initialisé, initialisation...');
                initCalendar();
            } else {
                console.log('Calendrier déjà initialisé, rechargement des événements...');
                calendar.refetchEvents();
            }
        } else if (target === '#all') {
            loadAllConsultations();
        }
    });

    // Gérer le bouton d'actualisation du calendrier
    $('#refreshCalendar').on('click', function() {
        console.log('Bouton actualiser cliqué');
        if (calendar) {
            console.log('Rechargement des événements du calendrier...');
            calendar.refetchEvents();
        } else {
            console.log('Calendrier non initialisé, initialisation...');
            initCalendar();
        }
    });

    // Gérer le clic sur le bouton de suppression
    $(document).on('click', '.delete-visite', function() {
        if(confirm('Êtes-vous sûr de vouloir supprimer cette consultation ?')) {
            const id = $(this).data('id');
            $.ajax({
                url: 'api/visites.php?id=' + id,
                type: 'DELETE',
                success: function() {
                    loadTodayConsultations();
                    loadAllConsultations();
                    // Recharger le calendrier si il existe
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                    showToast('Consultation supprimée avec succès');
                },
                error: function() {
                    showToast('Erreur lors de la suppression de la consultation', 'error');
                }
            });
        }
    });

    // Gérer le clic sur le bouton "Présent"
    $(document).on('click', '.mark-present', function() {
        const id = $(this).data('id');
        const button = $(this);
        
        // Désactiver le bouton pendant la requête
        button.prop('disabled', true);

        // Préparer les données pour la mise à jour
        const data = {
            status: 'present'
        };

        // Envoyer la requête PUT pour mettre à jour le statut
        $.ajax({
            url: 'api/visites.php?id=' + id,
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                // Recharger les deux tableaux
                loadTodayConsultations();
                loadAllConsultations();
                // Recharger le calendrier si il existe
                if (calendar) {
                    calendar.refetchEvents();
                }
                showToast('Statut mis à jour avec succès');
            },
            error: function() {
                // Réactiver le bouton en cas d'erreur
                button.prop('disabled', false);
                showToast('Erreur lors de la mise à jour du statut', 'error');
            }
        });
    });

    // Fonction pour afficher les toasts
    function showToast(message, type = 'success') {
        const toast = $('#successToast');
        toast.find('.toast-body').text(message);
        
        if (type === 'error') {
            toast.removeClass('bg-success').addClass('bg-danger');
        } else {
            toast.removeClass('bg-danger').addClass('bg-success');
        }
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    // Autocomplétion des patients
    let searchTimeout;
    $('#patient').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        const patientsList = $('#patientsList');

        if (searchTerm.length < 2) {
            patientsList.addClass('d-none').html('');
            return;
        }

        searchTimeout = setTimeout(() => {
            $.get('api/patients.php?search=' + encodeURIComponent(searchTerm), function(data) {
                if (data.length > 0) {
                    const html = data.map(patient => `
                        <a href="#" class="list-group-item list-group-item-action patient-item" 
                           data-id="${patient.id}" 
                           data-nom="${patient.nom}"
                           data-prenom="${patient.prenom}">
                            ${patient.nom} ${patient.prenom}
                        </a>
                    `).join('');
                    patientsList.html(html).removeClass('d-none');
                } else {
                    patientsList.addClass('d-none').html('');
                }
            });
        }, 300);
    });

    // Sélection d'un patient dans la liste
    $(document).on('click', '.patient-item', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const nom = $(this).data('nom');
        const prenom = $(this).data('prenom');
        
        $('#patient').val(`${nom} ${prenom}`);
        $('#patient_id').val(id);
        $('#patientsList').addClass('d-none').html('');
    });

    // Ouvrir le modal de création de patient
    $('#createPatientBtn').click(function() {
        const patientName = $('#patient').val().trim();
        if (patientName) {
            const nameParts = patientName.split(' ');
            const nom = nameParts[0] || '';
            const prenom = nameParts.slice(1).join(' ') || '';
            $('#nom').val(nom);
            $('#prenom').val(prenom);
        }
        $('#newPatientModal').modal('show');
    });

    // Sauvegarder un nouveau patient
    $('#savePatient').click(function() {
        const patientData = {
            nom: $('#nom').val(),
            prenom: $('#prenom').val(),
            telephone: $('#telephone').val()
        };

        $.ajax({
            url: 'api/patients.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(patientData),
            success: function(response) {
                $('#patient').val(`${patientData.nom} ${patientData.prenom}`);
                $('#patient_id').val(response.id);
                $('#newPatientModal').modal('hide');
                showToast('Patient créé avec succès');
            },
            error: function() {
                showToast('Erreur lors de la création du patient', 'error');
            }
        });
    });

    // Le gestionnaire pour sauvegarder un nouveau rendez-vous est maintenant dans dashboard.js
    // pour éviter les doublons d'événements

    // Fermer la liste des patients si on clique ailleurs
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#patient, #patientsList').length) {
            $('#patientsList').addClass('d-none').html('');
        }
    });
});
</script>
