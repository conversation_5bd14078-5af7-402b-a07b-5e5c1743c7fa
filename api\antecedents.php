<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'POST':
        // Ajouter un antécédent
        $data = json_decode(file_get_contents("php://input"));
        
        if(!empty($data->patient_id) && !empty($data->description)) {
            $query = "INSERT INTO antecedents (patient_id, description) VALUES (?, ?)";
            $stmt = $db->prepare($query);
            
            if($stmt->execute([$data->patient_id, $data->description])) {
                http_response_code(201);
                echo json_encode(["message" => "Antécédent ajouté avec succès"]);
            } else {
                http_response_code(503);
                echo json_encode(["message" => "Impossible d'ajouter l'antécédent"]);
            }
        } else {
            http_response_code(400);
            echo json_encode(["message" => "Données incomplètes"]);
        }
        break;

    case 'DELETE':
        // Supprimer un antécédent
        $id = isset($_GET['id']) ? $_GET['id'] : die();
        
        $query = "DELETE FROM antecedents WHERE id = ?";
        $stmt = $db->prepare($query);
        
        if($stmt->execute([$id])) {
            echo json_encode(["message" => "Antécédent supprimé avec succès"]);
        } else {
            http_response_code(503);
            echo json_encode(["message" => "Impossible de supprimer l'antécédent"]);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(["message" => "Méthode non autorisée"]);
        break;
} 