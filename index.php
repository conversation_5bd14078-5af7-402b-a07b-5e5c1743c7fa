<?php
include_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

// Récupérer les statistiques
$stats = [
    'total_patients' => 0,
    'total_honoraires' => 0,
    'consultations_today' => 0,
    'consultations_tomorrow' => 0
];

// Total patients
$query = "SELECT COUNT(*) as total FROM patients";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$stats['total_patients'] = $result['total'];

// Total honoraires
$query = "SELECT COALESCE(SUM(honoraire), 0) as total FROM visites WHERE status = 'fait'";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$stats['total_honoraires'] = $result['total'];

// Consultations aujourd'hui
$query = "SELECT COUNT(*) as total FROM visites WHERE DATE(date_visite) = CURDATE()";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$stats['consultations_today'] = $result['total'];

// Consultations demain
$query = "SELECT COUNT(*) as total FROM visites WHERE DATE(date_visite) = DATE_ADD(CURDATE(), INTERVAL 1 DAY)";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$stats['consultations_tomorrow'] = $result['total'];

$page_title = "Tableau de bord";
include 'includes/header.php';
?>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* Variables CSS pour la cohérence des couleurs */
    :root {
        --primary-color: #2563eb;
        --primary-light: #3b82f6;
        --primary-dark: #1d4ed8;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #06b6d4;
        --light-bg: #f8fafc;
        --border-color: #e2e8f0;
        --text-muted: #64748b;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    }

    /* Style général de la page */
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Header amélioré */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--shadow-lg);
    }

    .page-header h1 {
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
    }

    .page-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    /* Cards améliorées */
    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        background: white;
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-4px);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }

    /* Statistiques cards */
    .stats-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
        border-radius: 50%;
        transform: translate(30px, -30px);
    }

    .stats-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-lg);
        border-left-color: var(--primary-light);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }

    .stats-label {
        color: var(--text-muted);
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
        margin-bottom: 0.5rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        position: relative;
        z-index: 1;
    }

    /* Couleurs spécifiques pour chaque carte */
    .stats-card.patients {
        border-left-color: var(--primary-color);
    }

    .stats-card.patients .stats-number {
        color: var(--primary-color);
    }

    .stats-card.patients .stats-icon {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
    }

    .stats-card.revenue {
        border-left-color: var(--success-color);
    }

    .stats-card.revenue .stats-number {
        color: var(--success-color);
    }

    .stats-card.revenue .stats-icon {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
        color: white;
    }

    .stats-card.today {
        border-left-color: var(--info-color);
    }

    .stats-card.today .stats-number {
        color: var(--info-color);
    }

    .stats-card.today .stats-icon {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
        color: white;
    }

    .stats-card.tomorrow {
        border-left-color: var(--warning-color);
    }

    .stats-card.tomorrow .stats-number {
        color: var(--warning-color);
    }

    .stats-card.tomorrow .stats-icon {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
        color: white;
    }

    /* Tableaux améliorés */
    .table {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
        border: none;
        padding: 1rem;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: var(--light-bg);
        transform: scale(1.01);
    }

    .table tbody td {
        padding: 1rem;
        border-color: var(--border-color);
        vertical-align: middle;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.75rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    }

    .btn-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
    }

    .btn-outline-primary {
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        background: transparent;
    }

    .btn-outline-primary:hover {
        background: var(--primary-color);
        color: white;
    }

    .btn-outline-info {
        border: 2px solid var(--info-color);
        color: var(--info-color);
        background: transparent;
    }

    .btn-outline-info:hover {
        background: var(--info-color);
        color: white;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Badges de statut améliorés */
    .badge {
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
    }

    .bg-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%) !important;
    }

    .bg-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%) !important;
    }

    .bg-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%) !important;
    }

    /* Actions rapides card */
    .quick-actions-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-left: 4px solid var(--info-color);
    }

    /* Animation d'entrée pour les cartes */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: slideInUp 0.6s ease-out;
    }

    /* Améliorations des modals */
    .modal-content {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-lg);
    }

    .modal-header {
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 2px solid var(--border-color);
        border-radius: 0 0 1rem 1rem;
        padding: 1.5rem;
    }

    /* Améliorations des formulaires */
    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-muted);
        margin-bottom: 0.5rem;
    }

    /* Amélioration des toasts */
    .toast {
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .stats-number {
            font-size: 2rem;
        }

        .stats-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .card-body {
            padding: 1rem;
        }
    }
</style>

<!-- Header de la page amélioré -->
<div class="page-header">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1>
                    <i class="fas fa-tachometer-alt me-3"></i>
                    Tableau de bord
                </h1>
                <div class="subtitle">
                    Bienvenue dans votre espace de gestion médicale
                </div>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#newVisitModal">
                    <i class="fas fa-plus"></i> Nouveau rendez-vous
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-4">
    <!-- Cartes statistiques améliorées -->
    <div class="row g-4 mb-4">
        <div class="col-6 col-xl-3">
            <div class="card stats-card patients border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-label">Total Patients</div>
                            <div class="stats-number"><?php echo $stats['total_patients']; ?></div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-6 col-xl-3">
            <div class="card stats-card revenue border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-label">Total Honoraires</div>
                            <div class="stats-number"><?php echo number_format($stats['total_honoraires'], 0, ',', ' '); ?> <small style="font-size: 0.6em;">TND</small></div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-6 col-xl-3">
            <div class="card stats-card today border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-label">Consultations Aujourd'hui</div>
                            <div class="stats-number"><?php echo $stats['consultations_today']; ?></div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-6 col-xl-3">
            <div class="card stats-card tomorrow border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stats-label">Consultations Demain</div>
                            <div class="stats-number"><?php echo $stats['consultations_tomorrow']; ?></div>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides et Consultations -->
    <div class="row">
        <!-- Actions rapides -->
        <div class="col-lg-4 mb-4">
            <div class="card quick-actions-card border-0 h-100">
                <div class="card-header bg-transparent border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-info me-2"></i>
                        Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="patients.php" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>Nouveau Patient
                        </a>
                        <a href="consultations.php" class="btn btn-outline-info">
                            <i class="fas fa-calendar-check me-2"></i>Voir Consultations
                        </a>
                        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#newVisitModal">
                            <i class="fas fa-calendar-plus me-2"></i>Planifier RDV
                        </button>
                        <a href="patients.php" class="btn btn-outline-warning">
                            <i class="fas fa-users me-2"></i>Gérer Patients
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consultations d'aujourd'hui -->
        <div class="col-lg-8">
            <div class="card border-0 mb-4">
                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day text-primary me-2"></i>
                        Consultations d'aujourd'hui
                    </h5>
                    <a href="consultations.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-external-link-alt me-1"></i>
                        Voir tout
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Heure</th>
                                    <th>Patient</th>
                                    <th class="d-none d-md-table-cell">Type</th>
                                    <th>Statut</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $query = "SELECT v.*, p.nom, p.prenom 
                                         FROM visites v 
                                         JOIN patients p ON v.patient_id = p.id 
                                         WHERE DATE(v.date_visite) = CURDATE() 
                                         ORDER BY v.heure_visite ASC";
                                $stmt = $db->prepare($query);
                                $stmt->execute();
                                
                                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                    $statusClass = match($row['status']) {
                                        'rendez-vous' => 'warning',
                                        'present' => 'info',
                                        'fait' => 'success',
                                        default => 'secondary'
                                    };
                                    
                                    echo "<tr>";
                                    echo "<td class='px-3'>" . htmlspecialchars($row['heure_visite']) . "</td>";
                                    echo "<td class='text-truncate' style='max-width: 200px;'><a href='patient_details.php?id=" . $row['patient_id'] . "' class='text-decoration-none'>" . 
                                         htmlspecialchars($row['nom'] . " " . $row['prenom']) . "</a></td>";
                                    echo "<td class='d-none d-md-table-cell'>" . htmlspecialchars($row['type_visite']) . "</td>";
                                    echo "<td><span class='badge bg-" . $statusClass . "'>" . htmlspecialchars($row['status']) . "</span></td>";
                                    echo "<td class='text-end px-3'>";
                                    if($row['status'] === 'rendez-vous') {
                                        echo "<button class='btn btn-sm btn-success mark-present' data-id='" . $row['id'] . "'>";
                                        echo "<i class='fas fa-check'></i><span class='d-none d-md-inline'> Présent</span></button>";
                                    } else {
                                        echo "-";
                                    }
                                    echo "</td>";
                                    echo "</tr>";
                                }
                                
                                if($stmt->rowCount() === 0) {
                                    echo "<tr><td colspan='5' class='text-center text-muted py-4'>Aucune consultation aujourd'hui</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nouveau Rendez-vous -->
<div class="modal fade" id="newVisitModal" tabindex="-1" aria-labelledby="newVisitModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="newVisitModalLabel">
                    <i class="fas fa-calendar-plus me-2"></i>
                    Nouveau rendez-vous
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newVisitForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="patient" class="form-label">Patient</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="patient" required autocomplete="off" spellcheck="false">
                                    <input type="hidden" id="patient_id">
                                    <button class="btn btn-outline-secondary" type="button" id="createPatientBtn">
                                        <i class="fas fa-user-plus"></i>
                                    </button>
                                </div>
                                <div id="patientsList" class="list-group position-absolute w-100 d-none" style="z-index: 1000;"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type_visite" class="form-label">Type de consultation</label>
                                <select class="form-select" id="type_visite" required>
                                    <option value="cabinet" selected>Cabinet</option>
                                    <option value="domicile">Domicile</option>
                                    <option value="controle">Contrôle</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_visite" class="form-label">Date</label>
                                <input type="date" class="form-control" id="date_visite" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="heure_visite" class="form-label">Heure</label>
                                <select class="form-select" id="heure_visite" required>
                                    <?php
                                    for ($h = 8; $h < 19; $h++) {
                                        for ($m = 0; $m < 60; $m += 30) {
                                            $time = sprintf("%02d:%02d", $h, $m);
                                            echo "<option value='$time'>$time</option>";
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="saveVisit">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nouveau Patient -->
<div class="modal fade" id="newPatientModal" tabindex="-1" aria-labelledby="newPatientModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="newPatientModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    Nouveau patient
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newPatientForm">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" required>
                    </div>
                    <div class="mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" required>
                    </div>
                    <div class="mb-3">
                        <label for="telephone" class="form-label">Téléphone</label>
                        <input type="tel" class="form-control" id="telephone">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="savePatient">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body"></div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>



<?php include 'includes/footer.php'; ?> 