<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'POST':
        // Ajouter un traitement
        $data = json_decode(file_get_contents("php://input"));
        
        if(!empty($data->patient_id) && !empty($data->nom_medicament) && !empty($data->posologie) && !empty($data->date_debut)) {
            $query = "INSERT INTO traitements (patient_id, nom_medicament, posologie, date_debut, date_fin, statut) 
                      VALUES (?, ?, ?, ?, ?, 'en_cours')";
            $stmt = $db->prepare($query);
            
            if($stmt->execute([
                $data->patient_id,
                $data->nom_medicament,
                $data->posologie,
                $data->date_debut,
                $data->date_fin
            ])) {
                http_response_code(201);
                echo json_encode(["message" => "Traitement ajouté avec succès"]);
            } else {
                http_response_code(503);
                echo json_encode(["message" => "Impossible d'ajouter le traitement"]);
            }
        } else {
            http_response_code(400);
            echo json_encode(["message" => "Données incomplètes"]);
        }
        break;

    case 'PUT':
        if(isset($_GET['id']) && isset($_GET['action']) && $_GET['action'] === 'toggle_status') {
            // Changer le statut du traitement
            $id = $_GET['id'];
            
            // Vérifier le statut actuel
            $query = "SELECT statut FROM traitements WHERE id = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Inverser le statut
            $nouveau_statut = $result['statut'] === 'en_cours' ? 'termine' : 'en_cours';
            
            $query = "UPDATE traitements SET statut = ? WHERE id = ?";
            $stmt = $db->prepare($query);
            
            if($stmt->execute([$nouveau_statut, $id])) {
                echo json_encode(["message" => "Statut du traitement mis à jour avec succès"]);
            } else {
                http_response_code(503);
                echo json_encode(["message" => "Impossible de mettre à jour le statut"]);
            }
        } else {
            http_response_code(400);
            echo json_encode(["message" => "ID ou action non spécifié"]);
        }
        break;

    case 'DELETE':
        // Supprimer un traitement
        $id = isset($_GET['id']) ? $_GET['id'] : die();
        
        $query = "DELETE FROM traitements WHERE id = ?";
        $stmt = $db->prepare($query);
        
        if($stmt->execute([$id])) {
            echo json_encode(["message" => "Traitement supprimé avec succès"]);
        } else {
            http_response_code(503);
            echo json_encode(["message" => "Impossible de supprimer le traitement"]);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(["message" => "Méthode non autorisée"]);
        break;
} 