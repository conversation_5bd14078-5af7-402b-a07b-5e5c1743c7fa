<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../config/database.php';

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

// Gérer les requêtes OPTIONS pour CORS
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit();
}

switch($method) {
    case 'GET':
        if(isset($_GET['id'])) {
            // Récupérer une visite spécifique
            $query = "SELECT v.*, p.nom as nom_patient, p.prenom as prenom_patient 
                     FROM visites v 
                     JOIN patients p ON v.patient_id = p.id 
                     WHERE v.id = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$_GET['id']]);
            $visite = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if($visite) {
                echo json_encode($visite);
            } else {
                http_response_code(404);
                echo json_encode(["message" => "Visite non trouvée"]);
            }
        } elseif(isset($_GET['today'])) {
            // Récupérer les visites d'aujourd'hui
            $query = "SELECT v.*, p.nom as nom_patient, p.prenom as prenom_patient 
                     FROM visites v 
                     JOIN patients p ON v.patient_id = p.id 
                     WHERE DATE(v.date_visite) = CURDATE() 
                     ORDER BY v.heure_visite ASC";
            try {
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                // Log pour le débogage
                error_log("Requête aujourd'hui - Nombre de résultats : " . count($result));
                echo json_encode($result);
            } catch (PDOException $e) {
                error_log("Erreur SQL : " . $e->getMessage());
                http_response_code(500);
                echo json_encode(["message" => "Erreur lors de la récupération des visites", "error" => $e->getMessage()]);
            }
        } elseif(isset($_GET['patient_id'])) {
            // Récupérer les visites d'un patient spécifique
            $query = "SELECT v.*, p.nom as nom_patient, p.prenom as prenom_patient 
                     FROM visites v 
                     JOIN patients p ON v.patient_id = p.id 
                     WHERE v.patient_id = ? 
                     ORDER BY v.date_visite DESC, v.heure_visite ASC";
            try {
                $stmt = $db->prepare($query);
                $stmt->execute([$_GET['patient_id']]);
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode($result);
            } catch (PDOException $e) {
                error_log("Erreur SQL : " . $e->getMessage());
                http_response_code(500);
                echo json_encode(["message" => "Erreur lors de la récupération des visites", "error" => $e->getMessage()]);
            }
        } else {
            // Récupérer toutes les visites
            $query = "SELECT v.*, p.nom as nom_patient, p.prenom as prenom_patient 
                     FROM visites v 
                     JOIN patients p ON v.patient_id = p.id 
                     ORDER BY v.date_visite DESC, v.heure_visite ASC";
            try {
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                // Log pour le débogage
                error_log("Requête toutes les visites - Nombre de résultats : " . count($result));
                echo json_encode($result);
            } catch (PDOException $e) {
                error_log("Erreur SQL : " . $e->getMessage());
                http_response_code(500);
                echo json_encode(["message" => "Erreur lors de la récupération des visites", "error" => $e->getMessage()]);
            }
        }
        break;

    case 'POST':
        $data = json_decode(file_get_contents("php://input"));
        
        if(!isset($data->patient_id) || !isset($data->date_visite) || 
           !isset($data->heure_visite) || !isset($data->type_visite)) {
            http_response_code(400);
            echo json_encode(['message' => 'Données manquantes']);
            exit;
        }

        try {
            // Vérifier si le créneau est disponible
            $query = "SELECT id FROM visites
                     WHERE date_visite = ? AND heure_visite = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$data->date_visite, $data->heure_visite]);

            if ($stmt->rowCount() > 0) {
                http_response_code(409);
                echo json_encode(['message' => 'Ce créneau est déjà réservé']);
                exit;
            }

            // Vérifier si le même patient n'a pas déjà un rendez-vous à la même date/heure
            $query = "SELECT id FROM visites
                     WHERE patient_id = ? AND date_visite = ? AND heure_visite = ?";
            $stmt = $db->prepare($query);
            $stmt->execute([$data->patient_id, $data->date_visite, $data->heure_visite]);

            if ($stmt->rowCount() > 0) {
                http_response_code(409);
                echo json_encode(['message' => 'Ce patient a déjà un rendez-vous à cette date et heure']);
                exit;
            }

            // Ajouter la visite
            $query = "INSERT INTO visites (patient_id, date_visite, heure_visite, type_visite, notes, status) 
                     VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($query);
            $stmt->execute([
                $data->patient_id,
                $data->date_visite,
                $data->heure_visite,
                $data->type_visite,
                $data->notes ?? null,
                $data->status ?? 'rendez-vous'
            ]);
            
            http_response_code(201);
            echo json_encode(['id' => $db->lastInsertId()]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['message' => 'Erreur lors de l\'ajout de la visite']);
        }
        break;

    case 'PUT':
        if(!isset($_GET['id'])) {
            http_response_code(400);
            echo json_encode(['message' => 'ID de la visite manquant']);
            exit;
        }

        $data = json_decode(file_get_contents("php://input"));
        $id = $_GET['id'];

        // Debug: Log des données reçues
        error_log("PUT Request - ID: " . $id);
        error_log("PUT Request - Data: " . json_encode($data));

        try {
            $fields = [];
            $values = [];

            // Construire la requête dynamiquement pour tous les champs possibles
            if(isset($data->patient_id)) {
                $fields[] = "patient_id = ?";
                $values[] = $data->patient_id;
            }
            if(isset($data->date_visite)) {
                $fields[] = "date_visite = ?";
                $values[] = $data->date_visite;
            }
            if(isset($data->heure_visite)) {
                $fields[] = "heure_visite = ?";
                // Convertir HH:MM en HH:MM:SS si nécessaire
                $heure = $data->heure_visite;
                if (strlen($heure) === 5) { // Format HH:MM
                    $heure .= ':00'; // Ajouter les secondes
                }
                $values[] = $heure;
            }
            if(isset($data->status)) {
                $fields[] = "status = ?";
                $values[] = $data->status;
            }
            if(isset($data->type_visite)) {
                $fields[] = "type_visite = ?";
                $values[] = $data->type_visite;
            }
            if(isset($data->honoraire)) {
                $fields[] = "honoraire = ?";
                // Convertir chaîne vide en NULL pour le champ DECIMAL
                $honoraire = $data->honoraire;
                if ($honoraire === '' || $honoraire === null) {
                    $honoraire = null;
                } else {
                    $honoraire = floatval($honoraire);
                }
                $values[] = $honoraire;
            }
            if(isset($data->motif)) {
                $fields[] = "motif = ?";
                $values[] = $data->motif;
            }
            if(isset($data->histoire_maladie)) {
                $fields[] = "histoire_maladie = ?";
                $values[] = $data->histoire_maladie;
            }
            if(isset($data->examens_cliniques)) {
                $fields[] = "examens_cliniques = ?";
                $values[] = $data->examens_cliniques;
            }
            if(isset($data->examens_complementaires)) {
                $fields[] = "examens_complementaires = ?";
                $values[] = $data->examens_complementaires;
            }
            if(isset($data->diagnostic)) {
                $fields[] = "diagnostic = ?";
                $values[] = $data->diagnostic;
            }
            if(isset($data->cat)) {
                $fields[] = "cat = ?";
                $values[] = $data->cat;
            }
            if(isset($data->notes)) {
                $fields[] = "notes = ?";
                $values[] = $data->notes;
            }
            
            if(empty($fields)) {
                http_response_code(400);
                echo json_encode(['message' => 'Aucune donnée à mettre à jour']);
                exit;
            }

            $values[] = $id;
            $query = "UPDATE visites SET " . implode(", ", $fields) . " WHERE id = ?";

            // Debug: Log de la requête SQL
            error_log("PUT Query: " . $query);
            error_log("PUT Values: " . json_encode($values));

            $stmt = $db->prepare($query);
            $stmt->execute($values);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['message' => 'Visite mise à jour avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Visite non trouvée']);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            error_log("PUT Error: " . $e->getMessage());
            echo json_encode(['message' => 'Erreur lors de la mise à jour de la visite', 'error' => $e->getMessage()]);
        } catch (Exception $e) {
            http_response_code(500);
            error_log("PUT General Error: " . $e->getMessage());
            echo json_encode(['message' => 'Erreur générale', 'error' => $e->getMessage()]);
        }
        break;

    case 'DELETE':
        if(!isset($_GET['id'])) {
            http_response_code(400);
            echo json_encode(['message' => 'ID de la visite manquant']);
            exit;
        }

        try {
            $stmt = $db->prepare("DELETE FROM visites WHERE id = ?");
            $stmt->execute([$_GET['id']]);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['message' => 'Visite supprimée avec succès']);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Visite non trouvée']);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['message' => 'Erreur lors de la suppression de la visite']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['message' => 'Méthode non autorisée']);
        break;
} 