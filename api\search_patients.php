<?php
header('Content-Type: application/json');
require_once '../config/database.php';

if (!isset($_GET['term'])) {
    echo json_encode([]);
    exit;
}

$database = new Database();
$db = $database->getConnection();

$term = '%' . $_GET['term'] . '%';

try {
    $query = "SELECT id, nom, prenom 
              FROM patients 
              WHERE nom LIKE :term 
              OR prenom LIKE :term 
              ORDER BY nom, prenom 
              LIMIT 10";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':term', $term);
    $stmt->execute();
    
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($results);
    
} catch (PDOException $e) {
    echo json_encode([]);
} 