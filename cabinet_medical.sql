-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : mar. 13 mai 2025 à 23:51
-- Version du serveur : 10.4.27-MariaDB
-- Version de PHP : 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `cabinet_medical`
--

-- --------------------------------------------------------

--
-- Structure de la table `antecedents`
--

CREATE TABLE `antecedents` (
  `id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `description` text NOT NULL,
  `date_ajout` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `antecedents`
--

INSERT INTO `antecedents` (`id`, `patient_id`, `description`, `date_ajout`) VALUES
(1, 1, '7alto 7lila', '2025-05-13 20:05:29'),
(2, 1, 'rani mriiiiiidh mridh', '2025-05-13 20:09:19'),
(3, 1, 'ksjd fhkdjfhjskldfqjhsfks fjlsdkrudoivjfdf', '2025-05-13 20:11:40');

-- --------------------------------------------------------

--
-- Structure de la table `patients`
--

CREATE TABLE `patients` (
  `id` int(11) NOT NULL,
  `nom` varchar(50) NOT NULL,
  `prenom` varchar(50) NOT NULL,
  `date_naissance` date DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(20) NOT NULL,
  `numero_assurance_sociale` varchar(50) DEFAULT NULL,
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp(),
  `date_modification` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `patients`
--

INSERT INTO `patients` (`id`, `nom`, `prenom`, `date_naissance`, `adresse`, `telephone`, `numero_assurance_sociale`, `date_creation`, `date_modification`) VALUES
(1, 'mharsi', 'karim', '1900-01-01', 'rte gabes', '50999999', '99999', '2025-05-13 20:01:59', '2025-05-13 20:02:23'),
(2, 'noura', 'derbel', NULL, NULL, '50999999', NULL, '2025-05-13 21:07:44', '2025-05-13 21:07:44'),
(3, 'jamel', 'mharsi', NULL, NULL, '', NULL, '2025-05-13 21:15:35', '2025-05-13 21:15:35');

-- --------------------------------------------------------

--
-- Structure de la table `traitements`
--

CREATE TABLE `traitements` (
  `id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `nom_medicament` varchar(100) NOT NULL,
  `posologie` text NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date DEFAULT NULL,
  `statut` enum('en_cours','termine','annule') DEFAULT 'en_cours',
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `traitements`
--

INSERT INTO `traitements` (`id`, `patient_id`, `nom_medicament`, `posologie`, `date_debut`, `date_fin`, `statut`, `date_creation`) VALUES
(1, 1, 'ROSUVAST 10 10mg Comp.Pell. Bt 30', 'bla bla', '2000-01-01', '2025-05-23', 'termine', '2025-05-13 20:11:02'),
(2, 1, 'SYMBICORT TURBUHALER 200mcg/6mcg Pdre.P.Inhal.Bucc. Fl 120 Doses', ' kfshgkdfjghskdfjghdjk', '2025-05-13', '2025-05-13', 'termine', '2025-05-13 20:12:04');

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','doctor') NOT NULL DEFAULT 'doctor',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `full_name`, `role`, `created_at`, `last_login`) VALUES
(1, 'noora', '$2y$10$tmaTVcgbm.fONZLre9gMt.bdluAB3hUOjpEvf46rhZzZVk9F8O./y', '<EMAIL>', 'Administrateur', 'admin', '2025-05-13 21:35:50', '2025-05-13 21:43:15');

-- --------------------------------------------------------

--
-- Structure de la table `visites`
--

CREATE TABLE `visites` (
  `id` int(11) NOT NULL,
  `patient_id` int(11) NOT NULL,
  `date_visite` date NOT NULL,
  `heure_visite` time NOT NULL,
  `status` enum('rendez-vous','present','fait') DEFAULT 'rendez-vous',
  `type_visite` enum('cabinet','domicile','controle') NOT NULL,
  `honoraire` decimal(10,2) DEFAULT NULL,
  `motif` text DEFAULT NULL,
  `histoire_maladie` text DEFAULT NULL,
  `examens_cliniques` text DEFAULT NULL,
  `examens_complementaires` text DEFAULT NULL,
  `cat` text DEFAULT NULL COMMENT 'Conduite à tenir',
  `diagnostic` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp(),
  `date_modification` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `visites`
--

INSERT INTO `visites` (`id`, `patient_id`, `date_visite`, `heure_visite`, `status`, `type_visite`, `honoraire`, `motif`, `histoire_maladie`, `examens_cliniques`, `examens_complementaires`, `cat`, `diagnostic`, `notes`, `date_creation`, `date_modification`) VALUES
(1, 1, '2025-05-13', '21:51:00', 'fait', 'cabinet', '50.00', 'oki', 'kjghdjkfhgdk', 'jkvhdfjkh', 'sfhjkshkj', 'jhfjkgh dfjsg', 'ckjh djklfgshkd jf', NULL, '2025-05-13 20:27:36', '2025-05-13 20:51:39'),
(2, 1, '2025-05-13', '21:53:00', 'fait', 'cabinet', '70.00', 'ok', 'bbbbbbbb', 'ccccccccc', 'dddddddd', 'ffffffffffffff', 'eeeeeeeeeeee', NULL, '2025-05-13 20:36:37', '2025-05-13 20:53:56'),
(6, 2, '2025-05-13', '14:30:00', 'rendez-vous', 'cabinet', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'test', '2025-05-13 21:12:29', '2025-05-13 21:12:29'),
(7, 3, '2025-05-13', '10:30:00', 'present', 'cabinet', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'teba3 noura', '2025-05-13 21:16:09', '2025-05-13 21:16:16');

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `antecedents`
--
ALTER TABLE `antecedents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `patient_id` (`patient_id`);

--
-- Index pour la table `patients`
--
ALTER TABLE `patients`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `traitements`
--
ALTER TABLE `traitements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `patient_id` (`patient_id`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Index pour la table `visites`
--
ALTER TABLE `visites`
  ADD PRIMARY KEY (`id`),
  ADD KEY `patient_id` (`patient_id`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `antecedents`
--
ALTER TABLE `antecedents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `patients`
--
ALTER TABLE `patients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `traitements`
--
ALTER TABLE `traitements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `visites`
--
ALTER TABLE `visites`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `antecedents`
--
ALTER TABLE `antecedents`
  ADD CONSTRAINT `antecedents_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `traitements`
--
ALTER TABLE `traitements`
  ADD CONSTRAINT `traitements_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `visites`
--
ALTER TABLE `visites`
  ADD CONSTRAINT `visites_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
