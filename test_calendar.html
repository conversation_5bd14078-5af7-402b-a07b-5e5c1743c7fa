<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Calendrier</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test du Calendrier FullCalendar</h1>
        
        <div class="row">
            <div class="col-12">
                <div id="calendar"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Logs de débogage :</h3>
            <div id="debug-log" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/locales/fr.global.min.js"></script>
    
    <script>
        // Fonction pour logger dans la page
        function log(message) {
            console.log(message);
            const debugLog = document.getElementById('debug-log');
            debugLog.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        document.addEventListener('DOMContentLoaded', function() {
            log('DOM chargé');
            
            // Vérifier FullCalendar
            if (typeof FullCalendar === 'undefined') {
                log('❌ FullCalendar non chargé');
                return;
            } else {
                log('✅ FullCalendar chargé');
            }

            // Fonction pour charger les événements
            function loadEvents() {
                log('Chargement des événements...');
                
                return $.get('api/visites.php')
                    .then(function(data) {
                        log('Données reçues: ' + JSON.stringify(data));
                        
                        if (!data || data.length === 0) {
                            log('Aucune donnée reçue');
                            return [];
                        }
                        
                        const events = data.map(visite => {
                            const startDateTime = `${visite.date_visite}T${visite.heure_visite}:00`;
                            const startDate = new Date(startDateTime);
                            const endDate = new Date(startDate.getTime() + 30 * 60000);
                            
                            let backgroundColor;
                            switch(visite.status) {
                                case 'rendez-vous': backgroundColor = '#fbbf24'; break;
                                case 'present': backgroundColor = '#60a5fa'; break;
                                case 'fait': backgroundColor = '#34d399'; break;
                                default: backgroundColor = '#9ca3af';
                            }

                            const event = {
                                id: visite.id,
                                title: `${visite.nom_patient} ${visite.prenom_patient}`,
                                start: startDateTime,
                                end: endDate.toISOString(),
                                backgroundColor: backgroundColor
                            };
                            
                            log('Événement créé: ' + JSON.stringify(event));
                            return event;
                        });
                        
                        log('Total événements: ' + events.length);
                        return events;
                    })
                    .catch(function(error) {
                        log('❌ Erreur AJAX: ' + error.responseText);
                        return [];
                    });
            }

            // Initialiser le calendrier
            log('Initialisation du calendrier...');
            const calendarEl = document.getElementById('calendar');
            
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                locale: 'fr',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'timeGridWeek,timeGridDay'
                },
                slotMinTime: '08:00:00',
                slotMaxTime: '19:00:00',
                slotDuration: '00:30:00',
                height: 'auto',
                allDaySlot: false,
                weekends: false,
                events: loadEvents,
                eventDidMount: function(info) {
                    log('Événement monté: ' + info.event.title);
                },
                loading: function(isLoading) {
                    log('Chargement: ' + (isLoading ? 'en cours' : 'terminé'));
                }
            });
            
            log('Rendu du calendrier...');
            calendar.render();
            log('Calendrier rendu');
        });
    </script>
</body>
</html>
