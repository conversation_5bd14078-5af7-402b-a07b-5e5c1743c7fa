<?php
header('Content-Type: application/json');
require_once '../config/database.php';

// Vérifier si les données nécessaires sont présentes
if (!isset($_POST['visit_id']) || !isset($_POST['status'])) {
    echo json_encode(['success' => false, 'message' => 'Données manquantes']);
    exit;
}

$database = new Database();
$db = $database->getConnection();

$visit_id = intval($_POST['visit_id']);
$status = $_POST['status'];

// Valider le statut
$valid_statuses = ['rendez-vous', 'present', 'fait'];
if (!in_array($status, $valid_statuses)) {
    echo json_encode(['success' => false, 'message' => 'Statut invalide']);
    exit;
}

try {
    $query = "UPDATE visites SET status = :status WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':id', $visit_id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erreur lors de la mise à jour']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Erreur de base de données']);
} 