<?php
header('Content-Type: application/json');
require_once '../config/database.php';

// Vérifier si toutes les données nécessaires sont présentes
if (!isset($_POST['patient_id']) || !isset($_POST['date_visite']) || 
    !isset($_POST['heure_visite']) || !isset($_POST['type_visite'])) {
    echo json_encode(['success' => false, 'message' => 'Données manquantes']);
    exit;
}

$database = new Database();
$db = $database->getConnection();

$patient_id = intval($_POST['patient_id']);
$date_visite = $_POST['date_visite'];
$heure_visite = $_POST['heure_visite'];
$type_visite = $_POST['type_visite'];

try {
    // Vérifier si le patient existe
    $query = "SELECT id FROM patients WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $patient_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        echo json_encode(['success' => false, 'message' => 'Patient non trouvé']);
        exit;
    }
    
    // Vérifier si le créneau est disponible
    $query = "SELECT id FROM visites 
              WHERE date_visite = :date 
              AND heure_visite = :heure";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':date', $date_visite);
    $stmt->bindParam(':heure', $heure_visite);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => false, 'message' => 'Ce créneau est déjà réservé']);
        exit;
    }
    
    // Ajouter la visite
    $query = "INSERT INTO visites (patient_id, date_visite, heure_visite, type_visite, status) 
              VALUES (:patient_id, :date_visite, :heure_visite, :type_visite, 'rendez-vous')";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':patient_id', $patient_id);
    $stmt->bindParam(':date_visite', $date_visite);
    $stmt->bindParam(':heure_visite', $heure_visite);
    $stmt->bindParam(':type_visite', $type_visite);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erreur lors de l\'ajout du rendez-vous']);
    }
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Erreur de base de données']);
} 