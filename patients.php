<?php
include_once 'config/database.php';
$database = new Database();
$db = $database->getConnection();

$page_title = "Gestion des Patients";
include 'includes/header.php';
?>

<!-- CSS pour DataTables -->
<link href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* Variables CSS pour la cohérence des couleurs */
    :root {
        --primary-color: #2563eb;
        --primary-light: #3b82f6;
        --primary-dark: #1d4ed8;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #06b6d4;
        --light-bg: #f8fafc;
        --border-color: #e2e8f0;
        --text-muted: #64748b;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    }

    /* Style général de la page */
    body {
        background-color: var(--light-bg);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Header amélioré */
    .page-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--shadow-lg);
    }

    .page-header h1 {
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
    }

    .page-header .breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin-top: 1rem;
    }

    .page-header .breadcrumb-item a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
    }

    .page-header .breadcrumb-item.active {
        color: white;
    }

    /* Cards améliorées */
    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        background: white;
    }

    .card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .card-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0 !important;
        padding: 1.5rem;
    }

    /* Tableaux améliorés */
    .table {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table thead th {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
        border: none;
        padding: 1rem;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: var(--light-bg);
        transform: scale(1.01);
    }

    .table tbody td {
        padding: 1rem;
        border-color: var(--border-color);
        vertical-align: middle;
    }

    /* Boutons améliorés */
    .btn {
        border-radius: 0.75rem;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, var(--success-color) 0%, #34d399 100%);
    }

    .btn-warning {
        background: linear-gradient(135deg, var(--warning-color) 0%, #fbbf24 100%);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color) 0%, #f87171 100%);
    }

    .btn-info {
        background: linear-gradient(135deg, var(--info-color) 0%, #22d3ee 100%);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    /* Améliorations des modals */
    .modal-content {
        border: none;
        border-radius: 1rem;
        box-shadow: var(--shadow-lg);
    }

    .modal-header {
        border-bottom: 2px solid var(--border-color);
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 2px solid var(--border-color);
        border-radius: 0 0 1rem 1rem;
        padding: 1.5rem;
    }

    /* Améliorations des formulaires */
    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-muted);
        margin-bottom: 0.5rem;
    }

    /* Animation d'entrée pour les cartes */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .card {
        animation: slideInUp 0.6s ease-out;
    }

    /* Amélioration des toasts */
    .toast {
        border-radius: 0.75rem;
        box-shadow: var(--shadow-lg);
    }

    /* Statistiques cards */
    .stats-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-left: 4px solid var(--primary-color);
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .stats-label {
        color: var(--text-muted);
        font-weight: 500;
        text-transform: uppercase;
        font-size: 0.875rem;
        letter-spacing: 0.05em;
    }

    /* DataTables customization */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 1rem 0;
    }

    .dataTables_wrapper .dataTables_filter input {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.5rem 1rem;
    }

    .dataTables_wrapper .dataTables_length select {
        border: 2px solid var(--border-color);
        border-radius: 0.75rem;
        padding: 0.5rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .stats-number {
            font-size: 2rem;
        }
    }
</style>

<!-- Header de la page amélioré -->
<div class="page-header">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1>
                    <i class="fas fa-users me-3"></i>
                    Gestion des Patients
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="dashboard.php">
                                <i class="fas fa-home"></i> Accueil
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <i class="fas fa-users"></i> Patients
                        </li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addPatientModal">
                    <i class="fas fa-user-plus"></i> Nouveau Patient
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid py-4">
    <!-- Cartes de statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="stats-number" id="totalPatients">0</div>
                            <div class="stats-label">Total Patients</div>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="stats-number" id="newPatientsThisMonth">0</div>
                            <div class="stats-label">Nouveaux ce mois</div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="stats-number" id="consultationsToday">0</div>
                            <div class="stats-label">Consultations aujourd'hui</div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-calendar-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="stats-number" id="avgAge">0</div>
                            <div class="stats-label">Âge moyen</div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-birthday-cake fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table des patients -->
    <div class="card border-0">
        <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">
                <i class="fas fa-table text-primary me-2"></i>
                Liste des patients
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="patientsTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Date de Naissance</th>
                            <th>Téléphone</th>
                            <th>N° Assurance</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <!-- Ajout du conteneur de toasts -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <!-- Le message sera inséré ici -->
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajout Patient -->
<div class="modal fade" id="addPatientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    Ajouter un Patient
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPatientForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_naissance" class="form-label">Date de Naissance</label>
                            <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="telephone" name="telephone" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="2" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="numero_assurance_sociale" class="form-label">Numéro d'Assurance Sociale</label>
                        <input type="text" class="form-control" id="numero_assurance_sociale" name="numero_assurance_sociale" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modification Patient -->
<div class="modal fade" id="editPatientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    Modifier le Patient
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPatientForm">
                <input type="hidden" id="edit_id" name="id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="edit_nom" name="nom" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="edit_prenom" name="prenom" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_date_naissance" class="form-label">Date de Naissance</label>
                            <input type="date" class="form-control" id="edit_date_naissance" name="date_naissance" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_telephone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="edit_telephone" name="telephone" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="edit_adresse" name="adresse" rows="2" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_numero_assurance_sociale" class="form-label">Numéro d'Assurance Sociale</label>
                        <input type="text" class="form-control" id="edit_numero_assurance_sociale" name="numero_assurance_sociale" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer les modifications</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Confirmation Suppression -->
<div class="modal fade" id="deletePatientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Confirmer la suppression
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <div class="text-danger me-3">
                        <i class="fas fa-exclamation-circle fa-3x"></i>
                    </div>
                    <div>
                        <h6 class="mb-2">Attention !</h6>
                        <p class="mb-0">Êtes-vous sûr de vouloir supprimer ce patient ? Cette action est <strong>irréversible</strong> et supprimera également toutes les données associées (consultations, antécédents, traitements).</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
$(document).ready(function() {
    // Initialisation du toast
    var successToast = new bootstrap.Toast(document.getElementById('successToast'));

    // Fonction pour afficher le toast avec un message
    function showToast(message) {
        $('.toast-body').text(message);
        successToast.show();
    }

    // Fonction pour charger les statistiques
    function loadStatistics() {
        // Charger le nombre total de patients
        $.get('api/patients.php', function(data) {
            $('#totalPatients').text(data.length);

            // Calculer l'âge moyen
            if (data.length > 0) {
                let totalAge = 0;
                let validAges = 0;

                data.forEach(function(patient) {
                    if (patient.date_naissance) {
                        const birthDate = new Date(patient.date_naissance);
                        const today = new Date();
                        const age = Math.floor((today - birthDate) / (365.25 * 24 * 60 * 60 * 1000));
                        if (age > 0 && age < 150) {
                            totalAge += age;
                            validAges++;
                        }
                    }
                });

                if (validAges > 0) {
                    $('#avgAge').text(Math.round(totalAge / validAges) + ' ans');
                }
            }

            // Calculer les nouveaux patients ce mois
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();
            let newThisMonth = 0;

            data.forEach(function(patient) {
                if (patient.created_at) {
                    const createdDate = new Date(patient.created_at);
                    if (createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear) {
                        newThisMonth++;
                    }
                }
            });

            $('#newPatientsThisMonth').text(newThisMonth);
        });

        // Charger les consultations d'aujourd'hui
        const today = new Date().toISOString().split('T')[0];
        $.get('api/visites.php', function(data) {
            const todayConsultations = data.filter(function(visite) {
                return visite.date_visite === today;
            });
            $('#consultationsToday').text(todayConsultations.length);
        });
    }

    // Charger les statistiques au démarrage
    loadStatistics();

    // Initialisation de DataTables
    var table = $('#patientsTable').DataTable({
        ajax: {
            url: 'api/patients.php',
            dataSrc: ''
        },
        columns: [
            { data: 'id' },
            { data: 'nom' },
            { data: 'prenom' },
            { 
                data: 'date_naissance',
                render: function(data) {
                    return new Date(data).toLocaleDateString('fr-FR');
                }
            },
            { data: 'telephone' },
            { data: 'numero_assurance_sociale' },
            {
                data: null,
                render: function(data, type, row) {
                    return `
                        <a href="patient_details.php?id=${row.id}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="btn btn-sm btn-primary edit-patient" data-id="${row.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-patient" data-id="${row.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
        },
        dom: 'Bfrtip',
        buttons: [
            'copy', 'excel', 'pdf', 'print'
        ]
    });

    // Ajout d'un patient
    $('#addPatientForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: 'api/patients.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(Object.fromEntries(new FormData(this))),
            success: function(response) {
                $('#addPatientModal').modal('hide');
                table.ajax.reload();
                loadStatistics(); // Recharger les statistiques
                showToast('Patient ajouté avec succès');
            },
            error: function() {
                showToast('Erreur lors de l\'ajout du patient');
            }
        });
    });

    // Édition d'un patient
    $('#patientsTable').on('click', '.edit-patient', function() {
        var id = $(this).data('id');
        $.get('api/patients.php?id=' + id, function(data) {
            $('#edit_id').val(data.id);
            $('#edit_nom').val(data.nom);
            $('#edit_prenom').val(data.prenom);
            $('#edit_date_naissance').val(data.date_naissance);
            $('#edit_telephone').val(data.telephone);
            $('#edit_adresse').val(data.adresse);
            $('#edit_numero_assurance_sociale').val(data.numero_assurance_sociale);
            $('#editPatientModal').modal('show');
        });
    });

    // Soumission du formulaire d'édition
    $('#editPatientForm').on('submit', function(e) {
        e.preventDefault();
        var id = $('#edit_id').val();
        $.ajax({
            url: 'api/patients.php',
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(Object.fromEntries(new FormData(this))),
            success: function(response) {
                $('#editPatientModal').modal('hide');
                table.ajax.reload();
                loadStatistics(); // Recharger les statistiques
                showToast('Patient modifié avec succès');
            },
            error: function() {
                showToast('Erreur lors de la modification du patient');
            }
        });
    });

    // Suppression d'un patient
    var deleteId;
    $('#patientsTable').on('click', '.delete-patient', function() {
        deleteId = $(this).data('id');
        $('#deletePatientModal').modal('show');
    });

    $('#confirmDelete').on('click', function() {
        $.ajax({
            url: 'api/patients.php?id=' + deleteId,
            type: 'DELETE',
            success: function(response) {
                $('#deletePatientModal').modal('hide');
                table.ajax.reload();
                loadStatistics(); // Recharger les statistiques
                showToast('Patient supprimé avec succès');
            },
            error: function() {
                showToast('Erreur lors de la suppression du patient');
            }
        });
    });

    // Réinitialisation des formulaires lors de la fermeture des modals
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0].reset();
    });
});
</script> 