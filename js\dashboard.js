$(document).ready(function() {
    // Initialiser la date à aujourd'hui
    $('#date_visite').val(new Date().toISOString().split('T')[0]);
    
    // Fonction pour afficher les toasts
    function showToast(message, type = 'success') {
        const toast = $('#successToast');
        toast.find('.toast-body').text(message);
        
        if (type === 'error') {
            toast.removeClass('bg-success').addClass('bg-danger');
        } else {
            toast.removeClass('bg-danger').addClass('bg-success');
        }
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
    
    // Autocomplétion des patients
    let searchTimeout;
    $('#patient').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        const patientsList = $('#patientsList');

        if (searchTerm.length < 2) {
            patientsList.addClass('d-none').html('');
            return;
        }

        searchTimeout = setTimeout(() => {
            $.get('api/patients.php?search=' + encodeURIComponent(searchTerm), function(data) {
                if (data.length > 0) {
                    const html = data.map(patient => `
                        <a href="#" class="list-group-item list-group-item-action patient-item" 
                           data-id="${patient.id}" 
                           data-nom="${patient.nom}"
                           data-prenom="${patient.prenom}">
                            ${patient.nom} ${patient.prenom}
                        </a>
                    `).join('');
                    patientsList.html(html).removeClass('d-none');
                } else {
                    patientsList.addClass('d-none').html('');
                }
            });
        }, 300);
    });

    // Sélection d'un patient dans la liste
    $(document).on('click', '.patient-item', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const nom = $(this).data('nom');
        const prenom = $(this).data('prenom');
        
        $('#patient').val(`${nom} ${prenom}`);
        $('#patient_id').val(id);
        $('#patientsList').addClass('d-none').html('');
    });

    // Ouvrir le modal de création de patient
    $('#createPatientBtn').click(function() {
        const patientName = $('#patient').val().trim();
        if (patientName) {
            const nameParts = patientName.split(' ');
            const nom = nameParts[0] || '';
            const prenom = nameParts.slice(1).join(' ') || '';
            $('#nom').val(nom);
            $('#prenom').val(prenom);
        }
        $('#newPatientModal').modal('show');
    });

    // Sauvegarder un nouveau patient
    $('#savePatient').click(function() {
        const patientData = {
            nom: $('#nom').val(),
            prenom: $('#prenom').val(),
            telephone: $('#telephone').val()
        };

        $.ajax({
            url: 'api/patients.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(patientData),
            success: function(response) {
                $('#patient').val(`${patientData.nom} ${patientData.prenom}`);
                $('#patient_id').val(response.id);
                $('#newPatientModal').modal('hide');
                showToast('Patient créé avec succès');
            },
            error: function() {
                showToast('Erreur lors de la création du patient', 'error');
            }
        });
    });

    // Sauvegarder un nouveau rendez-vous
    $('#saveVisit').off('click').on('click', function() {
        const $button = $(this);

        // Vérifier si une requête est déjà en cours
        if ($button.prop('disabled')) {
            return;
        }

        const visitData = {
            patient_id: $('#patient_id').val(),
            date_visite: $('#date_visite').val(),
            heure_visite: $('#heure_visite').val(),
            type_visite: $('#type_visite').val(),
            notes: $('#notes').val(),
            status: 'rendez-vous'
        };

        if (!visitData.patient_id) {
            showToast('Veuillez sélectionner ou créer un patient', 'error');
            return;
        }

        if (!visitData.type_visite) {
            showToast('Veuillez sélectionner le type de consultation', 'error');
            return;
        }

        // Désactiver le bouton et changer le texte
        $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...');

        $.ajax({
            url: 'api/visites.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(visitData),
            success: function() {
                $('#newVisitModal').modal('hide');

                // Recharger les données sans recharger toute la page
                if (typeof loadTodayConsultations === 'function') {
                    loadTodayConsultations();
                }
                if (typeof loadAllConsultations === 'function') {
                    loadAllConsultations();
                }
                // Recharger le calendrier si il existe
                if (typeof calendar !== 'undefined' && calendar) {
                    calendar.refetchEvents();
                }

                showToast('Rendez-vous créé avec succès');
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Erreur lors de la création du rendez-vous';
                showToast(message, 'error');
            },
            complete: function() {
                // Réactiver le bouton
                $button.prop('disabled', false).html('Enregistrer');
            }
        });
    });

    // Réinitialiser l'état du bouton quand le modal s'ouvre
    $('#newVisitModal').on('show.bs.modal', function() {
        const $saveButton = $('#saveVisit');
        $saveButton.prop('disabled', false).html('Enregistrer');

        // Réinitialiser la date à aujourd'hui si elle n'est pas définie
        const $dateInput = $('#date_visite');
        if (!$dateInput.val()) {
            $dateInput.val(new Date().toISOString().split('T')[0]);
        }
    });

    // Fonction pour réinitialiser complètement le formulaire
    function resetNewVisitForm() {
        // Réinitialiser le formulaire
        $('#newVisitForm')[0].reset();

        // Vider tous les champs spécifiques
        $('#patient').val('');
        $('#patient_id').val('');
        $('#date_visite').val('');
        $('#heure_visite').val('');
        $('#type_visite').val('');
        $('#notes').val('');

        // Cacher la liste des patients
        $('#patientsList').addClass('d-none').html('');

        // Réinitialiser le bouton
        const $saveButton = $('#saveVisit');
        $saveButton.prop('disabled', false).html('Enregistrer');
    }

    // Nettoyer le formulaire à chaque fermeture du modal
    $('#newVisitModal').on('hidden.bs.modal', function() {
        resetNewVisitForm();
    });

    // Fonction pour réinitialiser complètement le formulaire nouveau patient
    function resetNewPatientForm() {
        // Réinitialiser le formulaire
        $('#newPatientForm')[0].reset();

        // Vider tous les champs spécifiques
        $('#nom').val('');
        $('#prenom').val('');
        $('#telephone').val('');
    }

    // Nettoyer le formulaire nouveau patient à chaque fermeture du modal
    $('#newPatientModal').on('hidden.bs.modal', function() {
        resetNewPatientForm();
    });

    // Gérer le clic sur le bouton "Présent"
    $('.mark-present').click(function() {
        const visitId = $(this).data('id');
        const button = $(this);
        
        // Désactiver le bouton pendant la requête
        button.prop('disabled', true);
        
        $.ajax({
            url: 'api/visites.php?id=' + visitId,
            type: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({ status: 'present' }),
            success: function() {
                location.reload(); // Recharger la page pour afficher le nouveau statut
                showToast('Statut mis à jour avec succès');
            },
            error: function() {
                button.prop('disabled', false);
                showToast('Erreur lors de la mise à jour du statut', 'error');
            }
        });
    });

    // Fermer la liste des patients si on clique ailleurs
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#patient, #patientsList').length) {
            $('#patientsList').addClass('d-none').html('');
        }
    });
}); 